/* Dell-branded main CSS styles - Minimalist version */

/* Global reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    --dell-blue: #0076CE;
    --dell-dark-blue: #0057A3;
    --dell-light-blue: #E6F2FF;
    --dell-orange: #FF5800;
    --dell-gray: #444444;
    --dell-light-gray: #F8F9FA;
    --dell-border: #E0E0E0;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: #F5F6F7;
    color: var(--dell-gray);
    line-height: 1.5;
    margin: 0;
    padding: 0;
    overflow-x: hidden; /* Prevent horizontal scroll */
    width: 100vw;
    box-sizing: border-box;
}

/* Override Bootstrap container constraints */
.container, .container-fluid, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
    max-width: none !important;
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Main container - FORCE full viewport width */
.main-container {
    width: 100vw !important;
    max-width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box;
    position: relative;
    left: 0;
    right: 0;
}

/* Dell Header - EXACT Reference Image Match */
.dell-header-ref {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    width: 100%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Shared container for all nav sections */
.dell-nav-container {
    width: 100%;
    max-width: none;
    padding: 0 20px;
    margin: 0;
    box-sizing: border-box;
}

/* Top blue navigation - exact reference colors */
.dell-top-nav {
    background: #4A90E2;
    color: white;
    padding: 5px 0;
    font-size: 12px;
}

.dell-nav-left {
    float: left;
}

.dell-nav-right {
    float: right;
}

.dell-nav-container::after {
    content: "";
    display: table;
    clear: both;
}

.dell-brand {
    font-weight: 600;
    font-size: 12px;
    color: white;
}

.dell-nav-item {
    color: white;
    text-decoration: none;
    font-size: 12px;
    margin-left: 20px;
    transition: opacity 0.2s;
}

.dell-nav-item:hover {
    opacity: 0.9;
    color: white;
}

/* Main Team Member Center bar - exact reference styling */
.dell-main-bar {
    background: #5BA0F2;
    color: white;
    padding: 8px 0;
}

.dell-main-left {
    float: left;
    display: flex;
    align-items: center;
    gap: 25px;
}

.dell-main-right {
    float: right;
    display: flex;
    align-items: center;
    gap: 15px;
}

.dell-main-title {
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.dell-search-container {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 3px;
    border: 1px solid #ddd;
}

.dell-search-input {
    border: none;
    padding: 6px 12px;
    font-size: 13px;
    outline: none;
    width: 250px;
    border-radius: 3px 0 0 3px;
}

.dell-search-btn {
    background: #f5f5f5;
    border: none;
    border-left: 1px solid #ddd;
    padding: 6px 10px;
    cursor: pointer;
    border-radius: 0 3px 3px 0;
}

.dell-main-item {
    color: white;
    text-decoration: none;
    font-size: 13px;
    transition: opacity 0.2s;
}

.dell-main-item:hover {
    opacity: 0.9;
    color: white;
}

.dell-user-avatar {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
}

/* Sub navigation - exact reference styling */
.dell-sub-nav {
    background: #6BB0FF;
    color: white;
    padding: 6px 0;
}

.dell-sub-item {
    color: white;
    text-decoration: none;
    font-size: 13px;
    margin-right: 25px;
    transition: opacity 0.2s;
}

.dell-sub-item:hover {
    opacity: 0.9;
    color: white;
}

/* Breadcrumb - exact reference styling */
.dell-breadcrumb {
    background: #f8f9fa;
    padding: 6px 0;
    border-bottom: 1px solid #e9ecef;
}

.dell-breadcrumb-link {
    color: #007bff;
    text-decoration: none;
    font-size: 13px;
}

.dell-breadcrumb-link:hover {
    text-decoration: underline;
}

.dell-breadcrumb-separator {
    color: #6c757d;
    font-size: 13px;
}

.dell-breadcrumb-current {
    color: #495057;
    font-size: 13px;
    font-weight: 500;
}

/* Grid system - More concise */
.col-3 {
    width: 25%;
    align-items: center;
    display: inline-flex;
}

.col-12 {
    display: block !important;
    flex: 0 0 auto;
    width: 100% !important;
    max-width: 100% !important;
}

/* Form elements - Enhanced visual appeal */
label.form-label {
    padding: 4px;
    display: block;
    font-weight: 500;
    color: var(--dell-gray);
    margin-bottom: 4px;
    font-size: 0.85rem;
}

.form-control {
    display: block;
    width: 100%;
    padding: 10px 14px;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--dell-gray);
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid var(--dell-border);
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: var(--dell-gray);
    background-color: #fff;
    border-color: var(--dell-blue);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(0, 118, 206, 0.1);
}

/* Enhanced dropdown styling with search functionality */
.dropdown-select {
    position: relative;
    width: 100%;
}

.dropdown-search {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--dell-border);
    border-radius: 4px 4px 0 0;
    font-size: 0.85rem;
    margin-bottom: -1px;
}

.dropdown-search:focus {
    outline: none;
    border-color: var(--dell-blue);
}

.dropdown-options {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--dell-border);
    border-radius: 0 0 4px 4px;
    background-color: white;
    z-index: 10;
}

.dropdown-option {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 0.85rem;
}

.dropdown-option:hover {
    background-color: var(--dell-light-blue);
    color: var(--dell-blue);
}

.dropdown-option.selected {
    background-color: var(--dell-blue);
    color: white;
}

/* Loading indicators - Minimalist */
.default-loading-spinner,
.custom-loading-spinner {
    display: block;
    position: fixed;
    top: 25vh;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

.custom-loading-spinner {
    border: 6px solid rgba(0, 118, 206, 0.1);
    border-top: 6px solid var(--dell-blue);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.custom-loading-text {
    text-align: center;
    font-size: 1rem;
    margin-top: 8px;
    color: var(--dell-blue);
}

/* Graph and visualization containers - More concise */
#graph-output {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 16px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* Button styles - Minimalist */
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    padding: 8px 14px;
    font-size: 0.85rem;
    line-height: 1.5;
    border-radius: 4px;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
                border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: var(--dell-blue);
    border: 1px solid var(--dell-blue);
}

.btn-primary:hover {
    background-color: var(--dell-dark-blue);
    border-color: var(--dell-dark-blue);
}

.btn-outline-primary {
    color: var(--dell-blue);
    background-color: transparent;
    border: 1px solid var(--dell-blue);
}

.btn-outline-primary:hover {
    color: white;
    background-color: var(--dell-blue);
}

/* Select2 customization for enhanced dropdown appearance */
.select2-container {
    width: 100% !important;
}

.select2-container--default .select2-selection--single {
    border-radius: 4px;
    border: 1px solid var(--dell-border);
    height: 38px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.select2-container--default.select2-container--focus .select2-selection--single,
.select2-container--default.select2-container--open .select2-selection--single {
    border-color: var(--dell-blue);
    box-shadow: 0 0 0 3px rgba(0, 118, 206, 0.1);
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 38px;
    padding-left: 12px;
    color: #495057;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
}

.select2-dropdown {
    border-color: var(--dell-blue);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid var(--dell-border);
    border-radius: 4px;
    padding: 8px 12px;
}

.select2-container--default .select2-search--dropdown .select2-search__field:focus {
    outline: none;
    border-color: var(--dell-blue);
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--dell-light-blue);
    color: var(--dell-blue);
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: var(--dell-blue);
    color: white;
}

/* Scrollbar customization for dropdowns */
.select2-results__options::-webkit-scrollbar {
    width: 8px;
}

.select2-results__options::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.select2-results__options::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.select2-results__options::-webkit-scrollbar-thumb:hover {
    background: #bbb;
}


/* Base styles */
:root {
    --dell-blue: #0076CE;
    --dell-dark-blue: #0057A3;
    --dell-light-blue: #E6F2FF;
    --dell-orange: #FF5800;
    --dell-gray: #444444;
    --dell-light-gray: #F8F9FA;
    --dell-border: #E0E0E0;
    --dell-success: #008A00;
    --dell-warning: #FFB900;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: #F5F6F7;
    color: var(--dell-gray);
    line-height: 1.5;
    padding-bottom: 40px;
    margin: 0;
}

/* Dashboard layout - TRUE full viewport width */
.dashboard-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 0;
    height: calc(100vh - 80px); /* Account for compact header */
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    position: relative;
    left: 0;
    right: 0;
}

/* Sidebar styles - fixed width for full layout */
.sidebar-filters {
    background-color: white;
    box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);
    padding: 15px;
    overflow-y: auto;
    position: sticky;
    top: 0;
    height: calc(100vh - 80px);
    width: 280px;
    min-width: 280px;
    max-width: 280px;
    box-sizing: border-box;
}

.sidebar-section {
    margin-bottom: 25px;
    border-bottom: 1px solid var(--dell-border);
    padding-bottom: 20px;
}

.sidebar-section:last-child {
    border-bottom: none;
}

.sidebar-section h2 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: var(--dell-blue);
}

/* Main content area - TRUE full width utilization */
.main-content {
    padding: 20px 30px;
    overflow-y: auto;
    width: 100%;
    max-width: none;
    box-sizing: border-box;
    min-width: 0;
    flex: 1;
    margin: 0;
}

/* New content wrapper for better structure - wider layout */
.content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 25px; /* Add space between sections */
    width: 100%;
    max-width: none;
}

/* Intelligent insights section - better use of wider space */
.intelligent-insights-section {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    padding: 25px;
    width: 100%;
    max-width: none;
}

.intelligent-insights-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    width: 100%;
}

/* Top applications section - wider layout */
.top-applications-section {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    padding: 25px;
    width: 100%;
    max-width: none;
}

.top-applications-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    width: 100%;
}


/* Organizational recommendation funnel */
.recommendation-funnel-section {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    padding: 16px;
    border: 1px solid var(--dell-border);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08); /* Slightly stronger shadow */

}

.recommendation-funnel-section h2 {
    color: var(--dell-blue);
    margin-bottom: 10px;
    font-size: 1.2rem;
    font-weight: 500;
}

.recommendation-funnel-section p {
    color: var(--dell-gray);
    font-size: 0.85rem;
    margin-bottom: 15px;
}

.funnel-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;
}

/* Funnel level styles */
.funnel-level {
    width: 100%;
    padding: 20px;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.funnel-level:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Active/clicked state styles */
.funnel-level.active {
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    border-left-width: 6px;
}

.funnel-level.l4.active {
    background-color: #D1E5FF;
    border-left-color: #0060A0;
}

.funnel-level.l5.active {
    background-color: #E0F0D0;
    border-left-color: #1E8E3E;
}

.funnel-level.l6l7.active {
    background-color: #FFF0CC;
    border-left-color: #FFB700;
}

/* Funnel level colors - using direct hex values */
.funnel-level.l4 {
    background-color: #E6F2FF; /* Dell light blue */
    border-left: 5px solid #0076CE; /* Dell blue */
}

.funnel-level.l5 {
    background-color: #E0F0D0; /* Light green */
    border-left: 5px solid #28a745; /* Green */
    width: 90%;
}

.funnel-level.l6l7 {
    background-color: #FFF0CC; /* Light yellow */
    border-left: 5px solid #ffc107; /* Yellow */
    width: 80%;
}

/* More distinct active states */
.funnel-level.l4.active {
    background-color: #D1E5FF;
    border-left-color: #0060A0;
}

.funnel-level.l5.active {
    background-color: #D0E8C0;
    border-left-color: #1E8E3E;
}

.funnel-level.l6l7.active {
    background-color: #FFE8B3;
    border-left-color: #E6A800;
}

/* Funnel header */
.funnel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.funnel-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.level-badge {
    background-color: rgba(0,0,0,0.1);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.funnel-level.l4 .level-badge {
    background-color: var(--dell-blue);
    color: white;
}

.funnel-level.l5 .level-badge {
    background-color: var(--dell-success);
    color: white;
}

.funnel-level.l6l7 .level-badge {
    background-color: var(--dell-warning);
    color: #212529;
}

.level-title {
    font-weight: 600;
    font-size: 1.1rem;
}

.funnel-personnel {
    display: flex;
    align-items: center;
    gap: 10px;
}

.personnel-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #666;
}

/* Metrics cards */
.funnel-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 15px 0;
}

.metric-card {
    background-color: rgba(255,255,255,0.7);
    padding: 12px;
    border-radius: 6px;
}

.metric-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 5px;
}

.metric-value {
    font-weight: 600;
    font-size: 1rem;
    color: #333;
}

.metric-detail {
    font-size: 0.8rem;
    color: #666;
    margin-top: 2px;
}

/* Success rate bars */
.success-rate-bar {
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    margin-top: 8px;
    overflow: hidden;
}

.success-rate-progress {
    height: 100%;
    border-radius: 4px;
}

.success-rate-high {
    background-color: var(--dell-success);
}

.success-rate-medium {
    background-color: var(--dell-warning);
}

.success-rate-low {
    background-color: #dc3545;
}

/* Application context */
.app-context {
    margin-top: 15px;
}

.app-context-title {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 8px;
    color: #555;
}

.app-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.app-tag {
    background-color: #f0f0f0;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    color: #555;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Implementation tasks */
.implementation-tasks {
    margin-top: 15px;
}

.tasks-title {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 8px;
    color: #555;
}

.task-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.task-name {
    font-size: 0.9rem;
}

.task-status {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
}

.task-status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.task-status-in-progress {
    background-color: #cce5ff;
    color: #004085;
}

.task-status-completed {
    background-color: #d4edda;
    color: #155724;
}

/* Funnel arrows */
.funnel-arrow {
    position: relative;
    height: 40px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.arrow-line {
    width: 2px;
    height: 20px;
    background-color: #999;
}

.arrow-head {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #999;
    margin-top: -1px;
}

.flow-context {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.funnel-arrow:hover .flow-context {
    opacity: 1;
}

.context-icon {
    color: var(--dell-blue);
    font-size: 0.9rem;
}

.context-text {
    font-size: 0.8rem;
    color: #666;
    white-space: nowrap;
}

/* Product rankings section */
.product-rankings-section {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    padding: 16px;
}

.product-rankings-section h2 {
    color: var(--dell-blue);
    margin-bottom: 14px;
    font-size: 1.2rem;
    font-weight: 500;
}

.product-rankings-section h3 {
    color: var(--dell-gray);
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 10px;
}

.ranking-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.ranking-column {
    display: flex;
    flex-direction: column;
}

.product-rankings {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.product-rank {
    display: flex;
    align-items: center;
    background-color: var(--dell-light-gray);
    border-radius: 6px;
    padding: 10px;
    transition: transform 0.2s;
}

.product-rank:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rank {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: var(--dell-blue);
    color: white;
    border-radius: 50%;
    font-weight: 600;
    font-size: 0.8rem;
    margin-right: 10px;
}

.product-details {
    flex: 1;
}

.product-name {
    font-weight: 500;
    color: var(--dell-gray);
    font-size: 0.9rem;
}

.product-stats {
    font-size: 0.75rem;
    color: #666;
    margin-top: 2px;
}

/* Product recommendations styling */
.product-recommendations {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.recommendation-item {
    display: flex;
    flex-direction: column;
    font-size: 0.85rem;
    color: var(--dell-gray);
    padding: 5px 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 8px;
}

.recommendation-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recommendation-title {
    font-weight: 600;
    color: var(--dell-blue);
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.recommendation-stats {
    font-size: 0.8rem;
    color: #555;
    margin-bottom: 4px;
    font-weight: 500;
}

.recommendation-text {
    font-size: 0.8rem;
    color: var(--dell-gray);
    line-height: 1.4;
}

/* Applications section - wider layout */
.applications-section {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    padding: 20px;
    width: 100%;
    max-width: none;
}

.applications-section h2 {
    color: var(--dell-blue);
    margin-bottom: 14px;
    font-size: 1.2rem;
    font-weight: 500;
}

/* Product usage ranking inside app cards */
.product-usage-ranking {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 8px;
}

.usage-rank-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    color: var(--dell-gray);
}

.usage-rank-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background-color: var(--dell-blue);
    color: white;
    border-radius: 50%;
    font-weight: 600;
    font-size: 0.7rem;
}

.usage-rank-item:nth-child(2) .usage-rank-number {
    background-color: var(--dell-success);
}

.usage-rank-item:nth-child(3) .usage-rank-number {
    background-color: var(--dell-warning);
}

.usage-rank-details {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.usage-rank-name {
    font-weight: 500;
}

.usage-rank-executions {
    color: #666;
}

/* Header - More minimalist */
.header {
    background-color: var(--dell-blue);
    color: white;
    padding: 10px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 0; /* Remove margin as we're using grid layout now */
    position: sticky;
    top: 0;
    z-index: 100;
}

.header h1 {
    font-size: 1.4rem;
    font-weight: 500;
}

#reportDate {
    font-size: 0.8rem;
    opacity: 0.9;
}

/* Containers and sections - FULL width without constraints */
.filters-section,
.summary-section,
.charts-row,
.dashboard-grid,
.recommendation-section {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin: 0 0 20px 0;
    padding: 25px;
    width: 100%;
    max-width: none;
    box-sizing: border-box;
}

.charts-row {
    display: flex;
    gap: 25px;
    width: 100%;
    max-width: none;
}

.chart-section {
    flex: 1;
    background-color: white;
    border-radius: 6px;
    padding: 25px;
    min-width: 0; /* Prevent flex items from overflowing */
    max-width: none;
}

.chart-container {
    height: 350px;
    width: 100%;
    max-width: none;
}

/* Filters */
.filters-section h2,
.summary-section h2,
.chart-section h2,
.recommendation-section h2 {
    color: var(--dell-blue);
    margin-bottom: 14px;
    font-size: 1.2rem;
    font-weight: 500;
}

.filters-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: flex-end;
    margin-bottom: 8px;
}

.filter-group {
    flex: 1 1 180px;
    margin-bottom: 16px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid var(--dell-border);
    transition: box-shadow 0.2s ease;
}

.filter-group:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.filter-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--dell-gray);
    font-size: 0.85rem;
}

/* Enhanced dropdown styling */
.form-select {
    width: 100%;
    padding: 10px 14px;
    border: 1px solid var(--dell-border);
    border-radius: 4px;
    font-size: 0.85rem;
    color: var(--dell-gray);
    background-color: white;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24'%3E%3Cpath fill='%23444' d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-select:focus {
    outline: none;
    border-color: var(--dell-blue);
    box-shadow: 0 0 0 3px rgba(0, 118, 206, 0.1);
}

/* Frequency tags */
.frequency-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.frequency-tag {
    background-color: var(--dell-light-gray);
    border: 1px solid var(--dell-border);
    border-radius: 16px;
    padding: 5px 10px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s;
}

.frequency-legend {
    display: none;
}

.frequency-tag.active {
    background-color: var(--dell-blue);
    color: white;
    border-color: var(--dell-blue);
}

.frequency-tag.high {
    background-color: var(--dell-light-blue);
    color: var(--dell-blue);
}

.frequency-tag.medium {
    background-color: #F2F9ED;
    color: var(--dell-success);
}

.frequency-tag.occasional {
    background-color: #FFF8E6;
    color: var(--dell-warning);
}

.frequency-tag.low {
    background-color: #FEF2F2;
    color: var(--dell-orange);
}

.frequency-tag.all {
    background-color: var(--dell-light-gray);
    color: var(--dell-gray);
}

.frequency-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Dell button styles */
.dell-button {
    background-color: var(--dell-blue);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 0.8rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: background-color 0.2s;
    height: 32px;
}

.dell-button:hover {
    background-color: var(--dell-dark-blue);
}

.filter-icon {
    width: 12px;
    height: 12px;
}

.clear-filters {
    background-color: transparent;
    color: var(--dell-blue);
    border: 1px solid var(--dell-blue);
    margin-top: 0;
    align-self: flex-end;
}

.clear-filters:hover {
    background-color: var(--dell-light-blue);
    color: var(--dell-blue);
}

.filters-actions {
    display: flex;
    align-items: flex-end;
    margin-left: auto;
}

/* Summary grid - full width utilization */
.summary-section {
    padding: 30px;
    width: 100%;
    max-width: none;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 25px;
    width: 100%;
    max-width: none;
}

.summary-item {
    background-color: white;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--dell-border);
    transition: all 0.2s;
}

.summary-item:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.summary-item strong {
    display: block;
    color: var(--dell-blue);
    margin-bottom: 10px;
    font-weight: 500;
    font-size: 0.85rem;
}

.summary-item-content {
    font-size: 0.8rem;
    line-height: 1.6;
    color: var(--dell-gray);
}

/* Recommendations - Material design style matching example */
.recommendation-cards {
    display: flex;
    gap: 16px;
    margin-top: 10px;
}

.recommendation-card {
    flex: 1;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.recommendation-card h3 {
    font-size: 1rem;
    margin-bottom: 4px;
    color: var(--dell-gray);
    font-weight: 500;
}

.recommendation-card p {
    color: var(--dell-gray);
    font-size: 0.75rem;
    margin-bottom: 14px;
    opacity: 0.8;
}

.recommendation-card.loyal {
    border-left: 4px solid var(--dell-blue);
    background-color: var(--dell-light-blue);
}

.recommendation-card.opportunity {
    border-left: 4px solid var(--dell-orange);
    background-color: #FFF5F0;
}

.recommendation-list {
    display: table;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 6px;
}

.recommendation-item {
    display: flex;
    flex-direction: column;
    font-size: 0.85rem;
    color: var(--dell-gray);
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 8px;
}

.recommendation-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.client-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.client-info .client-name {
    font-weight: 500;
    color: var(--dell-gray);
}

.client-info .execution-rate {
    font-weight: 500;
    color: var(--dell-blue);
    padding: 2px 6px;
    background-color: var(--dell-light-blue);
    border-radius: 4px;
    font-size: 0.8rem;
}

.client-recommendation {
    font-size: 0.8rem;
    color: #555;
    margin: 4px 0;
    line-height: 1.4;
    padding-left: 2px;
}

.client-recommendation strong {
    color: var(--dell-blue);
    font-weight: 600;
}

.client-product-stats {
    font-size: 0.75rem;
    color: #666;
    margin-top: 4px;
    background-color: #f9f9f9;
    padding: 4px 8px;
    border-radius: 4px;
}

.stat-label {
    font-weight: 500;
    color: #555;
}

.recommendation-section {
    padding-bottom: 6px;
}

/* Applications - Full width card layout optimization */
.applications-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 30px;
    margin: 25px 0;
    padding: 0;
    width: 100%;
    max-width: none;
}

/* Owner container section and pagination controls */
#ownerContainer {
    margin: 0 15px 15px 15px;
    display: none; /* Hide owner container as requested */
}

.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 15px 0;
    gap: 10px;
}

/* Bottom pagination style */
.bottom-pagination {
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid var(--dell-border);
}

.application-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 10px;
    border: 1px solid var(--dell-border);
}

.application-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.application-title {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--dell-gray);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.app-id {
    color: #777;
    font-weight: normal;
}

.application-owner {
    font-size: 0.85rem;
    color: #777;
    margin-top: -8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.l-level-tag {
    background-color: #e8f4f8;
    color: #0076CE;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    border: 1px solid rgba(0, 118, 206, 0.3);
    white-space: nowrap;
}

.priority-score {
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.execution-badge {
    font-size: 0.7rem;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.execution-badge.high {
    background-color: var(--dell-light-blue);
    color: var(--dell-blue);
}

.execution-badge.medium {
    background-color: #F2F9ED;
    color: var(--dell-success);
}

.execution-badge.occasional {
    background-color: #FFF8E6;
    color: var(--dell-warning);
}

.execution-badge.low {
    background-color: #FEF2F2;
    color: var(--dell-orange);
}

/* App metrics grid - tabular layout */
.app-metrics-grid {
    display: table;
    width: 100%;
    margin-top: 8px;
    border-collapse: separate;
    border-spacing: 0 2px;
}

.metrics-row {
    display: table-row;
}

.metrics-row:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.metric-label {
    display: table-cell;
    font-size: 0.75rem;
    color: #666;
    padding: 4px 0;
    text-align: left;
    font-weight: 500;
}

.metric-value {
    display: table-cell;
    text-align: right;
    padding: 4px 0;
    font-weight: 500;
    color: var(--dell-gray);
    font-size: 0.8rem;
}

/* Product section with styling similar to the example */
.product-section {
    margin-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 8px;
}

.product-header {
    font-size: 0.75rem;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
}

.product-value {
    font-size: 0.8rem;
    color: var(--dell-blue);
    line-height: 1.4;
    padding: 2px 0;
}

/* Pagination - More minimal */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 15px 0;
    gap: 10px;
}

.pagination-button {
    background-color: white;
    border: 1px solid var(--dell-border);
    color: var(--dell-blue);
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.75rem;
}

.pagination-button:hover:not([disabled]) {
    background-color: var(--dell-light-blue);
    border-color: var(--dell-blue);
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 0.75rem;
    color: var(--dell-gray);
}

/* L-level section - Organizational structure filters */
.l-level-section {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin: 0 15px 15px 15px;
    padding: 16px;
}

.l-level-section h2 {
    color: var(--dell-blue);
    margin-bottom: 14px;
    font-size: 1.2rem;
    font-weight: 500;
}

.l-level-section h3 {
    color: var(--dell-gray);
    margin-bottom: 10px;
    font-size: 1rem;
    font-weight: 500;
}

.l-level-search-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 15px;
}

.l-level-search-container label {
    font-weight: 500;
    color: var(--dell-gray);
    font-size: 0.85rem;
}

/* Text input for AppOwner search */
.l-level-search-container .form-control {
    width: 100%;
    padding: 10px 14px;
    border: 1px solid var(--dell-border);
    border-radius: 4px;
    font-size: 0.85rem;
    color: var(--dell-gray);
    transition: border-color 0.2s, box-shadow 0.2s;
}

.l-level-search-container .form-control:focus {
    outline: none;
    border-color: var(--dell-blue);
    box-shadow: 0 0 0 3px rgba(0, 118, 206, 0.1);
}

/* Organization grid layout */
.organization-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    margin-top: 15px;
}

/* Grid layout for L-level filters (vertical orientation) */
.l-level-filters-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 10px;
    border-right: 1px solid var(--dell-border);
    padding-right: 15px;
}

/* Product cross-recommendations section */
.product-cross-section {
    padding-left: 15px;
}

.product-cross-section p {
    color: var(--dell-gray);
    font-size: 0.85rem;
    margin-bottom: 15px;
}

/* Recommendation level tabs */
.recommendation-level-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--dell-border);
}

.level-tab {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--dell-gray);
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.level-tab:hover {
    color: var(--dell-blue);
}

.level-tab.active {
    color: var(--dell-blue);
    border-bottom: 2px solid var(--dell-blue);
}

/* Recommendation panels */
.recommendation-panel {
    display: none;
    margin-bottom: 15px;
}

.recommendation-panel.active {
    display: block;
}

.recommendation-panel h4 {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--dell-gray);
    margin-bottom: 10px;
}

.product-cross-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

/* Different colors for different recommendation levels */
#appowner-recs .cross-product-block {
    background-color: var(--dell-light-blue);
    border-left: 3px solid var(--dell-blue);
}

#same-level-recs .cross-product-block {
    background-color: #F2F9ED;
    border-left: 3px solid var(--dell-success);
}

#below-level-recs .cross-product-block {
    background-color: #FFF8E6;
    border-left: 3px solid var(--dell-warning);
}

.cross-product-block {
    flex: 1 1 200px;
    border-radius: 6px;
    padding: 12px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.cross-product-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.block-header {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.block-description {
    font-size: 0.8rem;
    color: var(--dell-gray);
}

/* Adjust text color for different recommendation types */
#same-level-recs .block-header {
    color: var(--dell-success);
}

#below-level-recs .block-header {
    color: var(--dell-warning);
}

/* Styling for L-level filter cards */
.l-level-filter {
    background-color: var(--dell-light-gray);
    border-radius: 6px;
    padding: 12px;
    border: 1px solid var(--dell-border);
}

.l-levels-submit-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

#applyLLevelFilters {
    min-width: 115px;
    justify-content: center;
    height: 36px;
    font-size: 0.9rem;
}

/* Secondary button style for clear filters */
.dell-button.secondary {
    background-color: transparent;


/* Product Clustering & Correlation Section */
.product-clustering-section {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    padding: 16px;
}

.product-clustering-section h2 {
    color: var(--dell-blue);
    margin-bottom: 10px;
    font-size: 1.2rem;
    font-weight: 500;
}

.product-clustering-section p {
    color: var(--dell-gray);
    font-size: 0.85rem;
    margin-bottom: 15px;
}

.cluster-visualization-container {
    min-height: 300px; /* Ensure space for the visualization */
    border: 1px dashed var(--dell-border);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--dell-light-gray);
    position: relative; /* For potential absolute positioning of elements */
}

.cluster-visualization-container .loading-placeholder {
    color: var(--dell-gray);
    font-style: italic;
}

#clusterOwnerName {
    font-weight: bold;
    color: var(--dell-dark-blue);
}

    color: var(--dell-blue);
    border: 1px solid var(--dell-blue);
    min-width: 80px;
    justify-content: center;
    height: 36px;
    font-size: 0.9rem;
}

.dell-button.secondary:hover {
    background-color: var(--dell-light-blue);
    color: var(--dell-blue);
}

/* Analysis View Tabs */
.analysis-tabs {
    margin-bottom: 20px;
}

.tab-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--dell-border);
    padding-bottom: 10px;
}

.tab-button {
    padding: 8px 16px;
    background: var(--dell-light-gray);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
    color: var(--dell-gray);
}

.tab-button:hover {
    background: var(--dell-light-blue);
    color: var(--dell-blue);
}

.tab-button.active {
    background: var(--dell-blue);
    color: white;
}

.tab-pane {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-pane.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Analysis View Styles */
.analysis-view {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.analysis-view h2 {
    color: var(--dell-blue);
    margin-top: 0;
    margin-bottom: 20px;
}

/* Responsive adjustments for full-width layout */
@media (max-width: 1400px) {
    .dell-top-container,
    .dell-main-container,
    .dell-secondary-container,
    .dell-breadcrumb-container {
        padding: 0 20px;
    }

    .dell-search-field {
        width: 240px;
    }
}

@media (max-width: 1200px) {
    .dell-top-right {
        gap: 20px;
    }

    .dell-search-field {
        width: 200px;
    }

    .dell-dropdown-section {
        gap: 25px;
    }
}

@media (max-width: 768px) {
    .dashboard-layout {
        grid-template-columns: 1fr;
        height: auto;
        width: 100vw;
    }

    .sidebar-filters {
        height: auto;
        position: relative;
        border-bottom: 1px solid var(--dell-border);
        width: 100%;
        min-width: auto;
        max-width: none;
    }

    .main-content {
        padding: 15px;
        width: 100%;
    }

    .tab-buttons {
        flex-wrap: wrap;
    }

    .tab-button {
        flex: 1 0 auto;
    }

    .dell-nav-right {
        display: none;
    }

    .dell-main-right {
        gap: 10px;
    }

    .dell-search-input {
        width: 150px;
    }

    .dell-sub-item {
        margin-right: 15px;
    }
}

@media (max-width: 480px) {
    .dell-nav-left-section {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .dell-search-box {
        width: 100%;
    }

    .dell-search-field {
        width: 100%;
    }

    .main-content {
        padding: 15px;
    }

    .dell-top-container,
    .dell-main-container,
    .dell-secondary-container,
    .dell-breadcrumb-container {
        padding: 0 15px;
    }
}


/* css app_product_integration */

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f6f7;
    color: #444;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
h1, h2 {
    color: #0076CE;
}
h2 {
    margin-top: 30px;
    margin-bottom: 15px;
}
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}
th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}
th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #0076CE;
}
tr:hover {
    background-color: #f5f5f5;
}
.badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
}
.badge-primary {
    background-color: #0076CE;
}
.badge-success {
    background-color: #28a745;
}
.badge-warning {
    background-color: #ffc107;
    color: #212529;
}
.success-rate {
    font-weight: 600;
}
.high {
    color: #28a745;
}
.medium {
    color: #ffc107;
}
.low {
    color: #dc3545;
}
.app-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}
.app-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px;
    transition: transform 0.2s;
}
.app-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}
.app-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: #0076CE;
}
.app-owner {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: #666;
}
.level-tag {
    background-color: #E6F2FF;
    color: #0076CE;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}
.app-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}
.metric-box {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
}
.metric-name {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 5px;
}
.metric-value {
    font-weight: 600;
    font-size: 1.1rem;
}
.product-section {
    margin-top: 15px;
}
.product-header {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 8px;
    color: #0076CE;
}
.product-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}
.product-tag {
    background-color: #E6F2FF;
    color: #0076CE;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
}
.product-tag .success {
    font-size: 0.7rem;
    background-color: #28a745;
    color: white;
    padding: 2px 5px;
    border-radius: 8px;
}
.remediation-steps {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    margin-top: 10px;
    font-size: 0.9rem;
}
.remediation-title {
    font-weight: 500;
    margin-bottom: 5px;
    color: #0076CE;
}
.inconsistency-section {
    background-color: #fff8e6;
    border-left: 4px solid #ffc107;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 6px 6px 0;
}
.inconsistency-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #856404;
}

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f6f7;
    color: #444;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}
h1 {
    color: #0076CE;
    margin-bottom: 10px;
}
.subtitle {
    color: #666;
    font-size: 1.1rem;
}
.section-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}
.section-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    transition: transform 0.2s;
    display: flex;
    flex-direction: column;
    height: 100%;
}
.section-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
}
.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #0076CE;
    margin-bottom: 10px;
}
.section-description {
    flex: 1;
    margin-bottom: 15px;
    line-height: 1.5;
}
.section-link {
    display: inline-block;
    background-color: #0076CE;
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    transition: background-color 0.2s;
    text-align: center;
}
.section-link:hover {
    background-color: #0057A3;
}
.key-findings {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 30px 0;
}
.key-findings h2 {
    color: #0076CE;
    margin-top: 0;
    margin-bottom: 15px;
}
.findings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}
.finding-item {
    background-color: white;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.finding-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: #0076CE;
}
.finding-description {
    font-size: 0.9rem;
    color: #666;
}
.summary-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}
.summary-table th, .summary-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}
.summary-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #0076CE;
}
.summary-table tr:hover {
    background-color: #f5f5f5;
}
footer {
    margin-top: 40px;
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f6f7;
    color: #444;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
h1, h2 {
    color: #0076CE;
    margin-top: 0;
}
p {
    line-height: 1.5;
    margin-bottom: 20px;
}

/* Visualization container */
.visualization-container {
    width: 100%;
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background-color: #f9f9f9;
    margin-bottom: 20px;
}

/* Controls and legend */
.controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    flex-wrap: wrap;
}
.view-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}
.view-button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f0f0f0;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}
.view-button.active {
    background-color: #0076CE;
    color: white;
    border-color: #0076CE;
}
.view-button:hover:not(.active) {
    background-color: #e0e0e0;
}
.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}
.filter-controls select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Tooltip */
.visualization-tooltip {
    position: absolute;
    visibility: hidden;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
    max-width: 250px;
    line-height: 1.4;
}

/* Node and link styling */
.node text {
    font-size: 10px;
    font-family: sans-serif;
    pointer-events: none;
}
.node.highlighted {
    stroke: #000;
    stroke-width: 2px;
}
.link {
    stroke-opacity: 0.6;
}
.link.highlighted {
    stroke-opacity: 1;
    stroke-width: 2px;
}

/* Legend */
.legend-container {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    z-index: 100;
}
.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}
.legend-color {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border-radius: 3px;
}
.legend-circle {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border-radius: 50%;
}
.legend-diamond {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    transform: rotate(45deg);
}
.legend-square {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
.legend-line {
    width: 24px;
    height: 2px;
    margin-right: 8px;
}

/* Pulse animation for urgent nodes */
.pulse-circle {
    animation: pulse 1.5s ease-in-out infinite;
}
@keyframes pulse {
    0% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 0.3; transform: scale(1.2); }
    100% { opacity: 0.5; transform: scale(1); }
}

/* Metrics section */
.metrics-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}
.metrics-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}
.metric-card {
    background-color: white;
    padding: 12px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.metric-value {
    font-size: 24px;
    font-weight: 600;
    color: #0076CE;
}
.metric-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

/* Loading indicator */
.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
}
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 118, 206, 0.2);
    border-radius: 50%;
    border-top-color: #0076CE;
    animation: spin 1s ease-in-out infinite;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error message */
.error-message {
    padding: 15px;
    background-color: #f8d7da;
    color: #721c24;
    border-radius: 4px;
    margin: 20px 0;
    text-align: center;
}

/* Insights section */
.insights-section {
    margin-top: 20px;
}
.insight-title {
    font-weight: 600;
    margin-bottom: 10px;
}
.insights-list {
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 15px;
}
.insight-item {
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
}
.insight-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}
.insight-name {
    font-weight: 600;
    color: #0076CE;
    margin-bottom: 4px;
}
.insight-description {
    font-size: 14px;
    line-height: 1.4;
}

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f6f7;
    color: #444;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
h1 {
    color: #0076CE;
    margin-bottom: 30px;
}
.funnel-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;
}
.funnel-level {
    width: 100%;
    padding: 20px;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s ease;
}
.funnel-level:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
.funnel-level.l4 {
    background-color: #E6F2FF;
    border-left: 5px solid #0076CE;
}
.funnel-level.l5 {
    background-color: #F2F9ED;
    border-left: 5px solid #28a745;
    width: 90%;
}
.funnel-level.l6l7 {
    background-color: #FFF8E6;
    border-left: 5px solid #ffc107;
    width: 80%;
}
.funnel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}
.funnel-title {
    display: flex;
    align-items: center;
    gap: 10px;
}
.level-badge {
    background-color: rgba(0,0,0,0.1);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}
.funnel-level.l4 .level-badge {
    background-color: #0076CE;
    color: white;
}
.funnel-level.l5 .level-badge {
    background-color: #28a745;
    color: white;
}
.funnel-level.l6l7 .level-badge {
    background-color: #ffc107;
    color: #212529;
}
.level-title {
    font-weight: 600;
    font-size: 1.1rem;
}
.funnel-personnel {
    display: flex;
    align-items: center;
    gap: 10px;
}
.personnel-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #666;
}
.funnel-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 15px 0;
}
.metric-card {
    background-color: rgba(255,255,255,0.7);
    padding: 12px;
    border-radius: 6px;
}
.metric-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 5px;
}
.metric-value {
    font-weight: 600;
    font-size: 1rem;
    color: #333;
}
.metric-detail {
    font-size: 0.8rem;
    color: #666;
    margin-top: 2px;
}
.success-rate-bar {
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    margin-top: 8px;
    overflow: hidden;
}
.success-rate-progress {
    height: 100%;
    border-radius: 4px;
}
.success-rate-high {
    background-color: #28a745;
}
.success-rate-medium {
    background-color: #ffc107;
}
.success-rate-low {
    background-color: #dc3545;
}
.app-context {
    margin-top: 15px;
}
.app-context-title {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 8px;
    color: #555;
}
.app-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}
.app-tag {
    background-color: #f0f0f0;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    color: #555;
    display: flex;
    align-items: center;
    gap: 5px;
}
.funnel-arrow {
    position: relative;
    height: 40px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.arrow-line {
    width: 2px;
    height: 20px;
    background-color: #999;
}
.arrow-head {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #999;
    margin-top: -1px;
}
.flow-context {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}
.funnel-arrow:hover .flow-context {
    opacity: 1;
}
.context-icon {
    color: #0076CE;
    font-size: 0.9rem;
}
.context-text {
    font-size: 0.8rem;
    color: #666;
    white-space: nowrap;
}
.implementation-tasks {
    margin-top: 15px;
}
.tasks-title {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 8px;
    color: #555;
}
.task-list {
    list-style: none;
    padding: 0;
    margin: 0;
}
.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}
.task-name {
    font-size: 0.9rem;
}
.task-status {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
}
.task-status-pending {
    background-color: #fff3cd;
    color: #856404;
}
.task-status-in-progress {
    background-color: #cce5ff;
    color: #004085;
}
.task-status-completed {
    background-color: #d4edda;
    color: #155724;
}

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f6f7;
    color: #444;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
h1, h2 {
    color: #0076CE;
}
h2 {
    margin-top: 30px;
    margin-bottom: 15px;
}
.ranking-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}
.product-rank {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    transition: transform 0.2s;
}
.product-rank:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.rank {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background-color: #0076CE;
    color: white;
    border-radius: 50%;
    font-weight: 600;
    margin-right: 15px;
}
.product-details {
    flex: 1;
}
.product-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
}
.product-stats {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}
.product-apps {
    font-size: 0.85rem;
    color: #0076CE;
}
.recommendation-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    transition: transform 0.2s;
}
.recommendation-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.recommendation-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 8px;
    color: #0076CE;
}
.badge {
    background-color: #28a745;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}
.recommendation-stats {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}
.recommendation-text {
    font-size: 0.9rem;
    line-height: 1.4;
}
.app-dependency {
    background-color: #E6F2FF;
    padding: 10px;
    border-radius: 6px;
    margin-top: 10px;
}
.app-dependency-title {
    font-weight: 500;
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: #0076CE;
}
.app-dependency-text {
    font-size: 0.85rem;
}
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}
th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}
th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #0076CE;
}
tr:hover {
    background-color: #f5f5f5;
}
.success-rate {
    font-weight: 600;
}
.high {
    color: #28a745;
}
.medium {
    color: #ffc107;
}
.low {
    color: #dc3545;
}

/* Remediation Statistics - wider layout */
.remediation-stats-section {
    margin: 30px 0;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
    width: 100%;
    max-width: none;
}

.remediation-stats-section h3 {
    margin-bottom: 20px;
    color: #495057;
    font-size: 1.2em;
}

.remediation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
    width: 100%;
}

.remediation-item {
    background: white;
    padding: 12px;
    border-radius: 6px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.remediation-item strong {
    display: block;
    color: #495057;
    font-size: 0.85em;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.remediation-item span {
    font-size: 1.2em;
    font-weight: bold;
    color: #007bff;
}

/* Top Applications */
.top-applications-section {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
}

.top-applications-section h3 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.2em;
}

.top-applications-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.top-app-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.top-app-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.top-app-rank {
    background: #007bff;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.top-app-details {
    flex: 1;
}

.top-app-name {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.top-app-owner {
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 8px;
}

.top-app-metrics {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.top-app-metrics .metric {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    color: #495057;
}

/* Intelligent Insights Section - Minimalistic Version */
.intelligent-insights-section {
    background: white;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}

.intelligent-insights-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 16px;
}

.insight-card {
    padding: 16px;
    border-radius: 6px;
    border-left: 3px solid;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    transition: box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.insight-card:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.12);
}

.insight-card.critical {
    border-left-color: #dc3545;
}

.insight-card.high {
    border-left-color: #fd7e14;
}

.insight-card.medium {
    border-left-color: #ffc107;
}

.insight-card.low {
    border-left-color: #28a745;
}

.insight-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.insight-title {
    font-weight: 600;
    font-size: 15px;
    color: #333;
    line-height: 1.3;
    margin: 0;
    flex: 1;
}

.insight-badges {
    display: flex;
    gap: 6px;
    align-items: center;
}

.priority-badge {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge.critical {
    background: #dc3545;
    color: white;
}

.priority-badge.high {
    background: #fd7e14;
    color: white;
}

.priority-badge.medium {
    background: #ffc107;
    color: #333;
}

.priority-badge.low {
    background: #28a745;
    color: white;
}

.confidence-badge {
    font-size: 10px;
    color: #666;
    background: #f8f9fa;
    padding: 2px 5px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.insight-description {
    color: #555;
    line-height: 1.4;
    font-size: 14px;
    margin: 0;
}

/* Contact Section - Minimalistic */
.insight-contacts {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.contacts-label {
    font-weight: 500;
    font-size: 12px;
    color: #666;
    min-width: fit-content;
}

.contacts-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.contact-tag {
    background: #0076CE;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 500;
}

.contact-more {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 500;
}

/* Key Actions Section - Minimalistic */
.key-actions {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 10px;
    border-left: 3px solid #0076CE;
}

.action-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #e9ecef;
}

.action-item:last-child {
    border-bottom: none;
}

.action-main {
    display: flex;
    gap: 8px;
    align-items: center;
    flex: 1;
}

.app-name {
    font-weight: 600;
    color: #333;
    font-size: 12px;
}

.owner-name {
    color: #666;
    font-size: 11px;
    background: #e9ecef;
    padding: 1px 4px;
    border-radius: 4px;
}

.product-name {
    color: #0076CE;
    font-size: 11px;
    font-weight: 500;
}

.confidence {
    font-size: 10px;
    color: #28a745;
    font-weight: 600;
    background: #d4edda;
    padding: 2px 4px;
    border-radius: 4px;
}

/* Quick Actions Section - Minimalistic */
.quick-actions {
    margin-top: 8px;
}

.actions-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.actions-list li {
    padding: 2px 0;
    font-size: 12px;
    color: #555;
    line-height: 1.3;
    position: relative;
    padding-left: 12px;
}

.actions-list li:before {
    content: "•";
    color: #0076CE;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.more-indicator {
    color: #666;
    font-style: italic;
    font-size: 11px;
}

/* Footer Section */
.insight-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

.impact-info,
.timeline-info {
    font-size: 11px;
    color: #666;
}

.impact-info {
    font-weight: 500;
}

.more-actions {
    color: #666;
    font-style: italic;
    font-size: 11px;
    text-align: center;
    padding: 4px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-top: 4px;
}

.no-insights {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
    grid-column: 1 / -1;
}



/* Responsive adjustments for minimalistic insights */
@media (max-width: 768px) {
    .intelligent-insights-container {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .insight-card {
        padding: 12px;
    }

    .insight-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }

    .insight-badges {
        flex-direction: row;
        align-items: center;
        gap: 6px;
    }

    .contacts-list {
        flex-direction: column;
        gap: 3px;
    }

    .action-main {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .insight-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}